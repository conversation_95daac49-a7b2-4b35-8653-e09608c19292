import { type Locator, type Page, expect } from '@playwright/test';
import { BasePage } from '../BasePage';

export abstract class AssistantBaseStep extends BasePage {
  readonly spinner: Locator;
  readonly backButton: Locator;
  readonly nextButton: Locator;
  readonly saveForLaterButton: Locator;

  constructor(page: Page) {
    super(page);
    this.spinner = page.locator('.spinner');
    this.backButton = page.getByRole('button', { name: 'Back' });
    this.nextButton = page.getByRole('button', { name: 'Next' });
    this.saveForLaterButton = page.getByRole('button', { name: 'Save for Later' });
  }

  async getErrorFor(input: Locator): Promise<Locator> {
    const id = await input.getAttribute('aria-describedby');
    if (!id) {
      throw new Error(`Missing aria-describedby on input: ${await input.evaluate(el => el.outerHTML)}`);
    }
    return this.page.locator(`#${id}`);
  }

  async expectErrorFor(input: Locator, { timeout } = {} as {timeout?: number}): Promise<void> {
    const error = await this.getErrorFor(input);
    await expect(error).toBeVisible({ timeout });
  }

}
