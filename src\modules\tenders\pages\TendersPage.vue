<template>
  <PageContainerWrapper>
    <div class="w-full transition-all duration-500 ease-[var(--ease-out-3)]">
      <main class="flex justify-center items-center" />
      <div class="flex justify-center">
        <div role="list" aria-label="Tenders list" class="grid gap-4 w-full grid-cols-[repeat(auto-fill,minmax(680px,1fr))]">
          <TenderCard
            v-for="tender in tenders"
            :id="tender.id"
            :key="tender.id"
            :author="tender.created_by"
            :date="tender.created_at"
            :title="tender.name"
            :description="tender.description"
            :region="tender.region"
            :main-industry="tender.main_industry"
            :invited-vendors="tender.invitedVendors"
            :views="tender.views"
            :applications="tender.applications"
            :positions="tender.positions"
            role="listitem"
          />
          <div v-if="!tenders.length" class="rounded-3xl bg-nio-grey-background py-4 pl-4 pr-1 col-span-full text-center">
            <div>
              You haven't created any tenders yet. It's great to get started using our
              <router-link class="text-nio-blue-800 hover:text-nio-blue-800/80" :to="{name: routeMap.assistant.name}">
                Assistant!
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PageContainerWrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import TenderCard from '@/modules/tenders/components/TenderCard.vue';
import { useTenderStore } from '@/modules/tenders/stores/tenders-store';
import type { Tender } from '@/modules/tenders/types/tenders-types';
import PageContainerWrapper from '@/common/components/PageContainerWrapper.vue';
import { routeMap } from '@/modules/assistant/routes';
import { handleTenderError } from '@/modules/tenders/handlers/error';

const tenderStore = useTenderStore();
const tenders = ref<Tender[]>([]);

try {
  tenders.value = await tenderStore.fetchTenders();
} catch (error) {
  console.error('Error fetching tenders:', error);
  handleTenderError(error);
}
</script>
