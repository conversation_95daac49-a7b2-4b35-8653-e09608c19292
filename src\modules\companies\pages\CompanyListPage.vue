<script setup lang="ts">
import { ref, watch } from 'vue';
import CompanyList from '@/modules/companies/components/CompanyList.vue';
import PageContainerWrapper from '@/common/components/PageContainerWrapper.vue';
import CompanyListSkeleton from '@/modules/companies/components/skeletons/CompanyListSkeleton.vue';
import CompanyListTableSkeleton from '@/modules/companies/components/skeletons/CompanyListTableSkeleton.vue';
import CompanyFilter from '@/modules/companies/components/CompanyFilter.vue';

const searchQuery = ref('');
const isList = ref(localStorage.getItem('isList') === 'true');

watch(isList, newValue => {
  localStorage.setItem('isList', newValue.toString());
});
</script>

<template>
  <PageContainerWrapper transparent>
    <div class="mb-5">
      <!-- Filter -->
      <CompanyFilter v-model:search-query="searchQuery" v-model:list-view="isList" />
    </div>
    <div class="relative">
      <Suspense>
        <CompanyList :search-query="searchQuery" :list-view="isList" />
        <template #fallback>
          <div v-if="true" class="absolute top-0 left-0 w-full h-full">
            <CompanyListTableSkeleton v-if="isList" />
            <CompanyListSkeleton v-else />
          </div>
        </template>
      </Suspense>
    </div>
    <router-view />
  </PageContainerWrapper>
</template>

