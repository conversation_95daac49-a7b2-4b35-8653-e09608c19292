<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import CompanyCard from '@/modules/companies/components/cards/CompanyCard.vue';
import { useQuestionStore } from '@/modules/companies/stores/question-store';
import { useAuthStore } from '@/modules/auth/stores/auth-store';
import type { Company } from '@/modules/companies/types/company';
import NioTable from '@/common/components/UI/table/NioTable.vue';
import PageCard from '@/common/components/PageCard.vue';
import CompanyTableRow from '@/modules/companies/components/cards/CompanyTableRow.vue';
import NioPagination from '@/common/components/NioPagination.vue';

const questionStore = useQuestionStore();
const authStore = useAuthStore();
const companies = ref<Company[]>([]);

const currentPage = defineModel<number>({ required: false, default: 1 });
const lastPage = ref(1);
const totalItems = ref(1);
const perPage = ref(1);

const props = defineProps<{
  searchQuery: string;
  listView?: boolean;
}>();

const reorderedCompanies = computed((): Company[] => {
  const singleWidth = 1;
  const maxColumns = 4;

  const result: Company[] = [];
  let currentRowWidth = 0;

  companies.value.forEach(company => {
    const itemWidth = singleWidth;

    if (currentRowWidth + itemWidth > maxColumns) {
      currentRowWidth = 0;
    }

    result.push(company);
    currentRowWidth += itemWidth;
  });

  return result;
});

const columns = computed(() => {
  return [
    { key: 'name', label: 'Vendor', width: 2 },
    { key: 'location', label: 'Location', width: 1 },
    { key: 'category', label: 'Category', width: 1 },
    { key: 'status', label: 'Status', width: 1 },
    { key: 'actions', label: '', width: 0.5, align: 'right' as const },
  ];
});

const fetchVendors = async() => {
  try {
    const response = await questionStore.getPaginatedVendors(props.searchQuery, currentPage.value);

    if (props.listView || currentPage.value === 1) {
      companies.value = Object.values(response.data);
    } else {
      companies.value = [...companies.value, ...Object.values(response.data) as Company[]];
    }

    lastPage.value = response.meta.last_page;
    totalItems.value = response.meta.total;
    perPage.value = response.meta.per_page;
  } catch (error) {
    console.error('Error fetching companies:', error);
  }
};

const onDeleteCompany = async(companyId: any) => {
  try {
    await authStore.removeCompany(companyId);
    companies.value = companies.value.filter(company => company.id !== companyId);
  } catch (error) {
    console.error(error);
  }
};

watch(() => props.searchQuery, async() => {
  currentPage.value = 1;
  await fetchVendors();
});

watch(() => props.listView, async() => {
  currentPage.value = 1;
  await fetchVendors();
});

watch(() => currentPage.value, async() => {
  await fetchVendors();
});

await fetchVendors();
</script>

<template>
  <Transition
    appear
    enter-active-class="transition-opacity duration-150 ease-in"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition-opacity duration-0 ease-in"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div v-show="!listView">
      <div class="grid gap-x-3 gap-y-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-5">
        <CompanyCard
          v-for="(company, index) in reorderedCompanies"
          :id="company.id"
          :key="index"
          :company="company"
          @delete-company="onDeleteCompany"
        />
        <div v-show="reorderedCompanies.length === 0" class="rounded-3xl bg-nio-grey-background py-4 pl-4 pr-1 col-span-full text-center">
          <div>
            No vendors found
          </div>
        </div>
      </div>
      <div v-if="currentPage < lastPage" class="w-full flex justify-center text-center mt-3">
        <button
          class="px-5 py-2 rounded-full text-sm transition-all duration-200 flex items-center gap-1.5 cursor-pointer bg-nio-grey-900 text-white transform hover:opacity-90 active:scale-95"
          @click="currentPage++"
        >
          Load more
        </button>
      </div>
    </div>
  </Transition>
  <PageCard v-show="listView">
    <NioTable :data="reorderedCompanies" :columns="columns">
      <CompanyTableRow
        v-for="(company, index) in reorderedCompanies"
        :key="index"
        :company="company"
        :columns="columns"
        @delete-company="onDeleteCompany"
      />
    </NioTable>
    <div class="text-nio-grey-700 text-sm py-5 text-center w-full">
      <div v-if="reorderedCompanies.length === 0" class="rounded-3xl bg-nio-grey-background py-4 pl-4 pr-1 col-span-full text-center">
        <div>
          No vendors found
        </div>
      </div>
      <div v-else class="w-full flex justify-center text-center mt-5">
        <div class="mt-auto mb-0 mx-auto nio-pagination">
          <NioPagination
            v-model="currentPage"
            :items-length="totalItems"
            :items-per-page="perPage"
          />
        </div>
      </div>
    </div>
  </PageCard>
</template>
