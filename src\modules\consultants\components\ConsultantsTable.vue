<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import ConsultantsTableEntry from './ConsultantsTableEntry.vue';
import { ConsultantTableType, type CandidateRow, type CandidatesResponse } from '../types/consultants-types';
import { nioAxios } from '@/axios';
import type { NioTableColumn } from '@/common/types/components';
import NioTable from '@/common/components/UI/table/NioTable.vue';
import NioTableRow from '@/common/components/UI/table/NioTableRow.vue';
import AssignToManagerPopover from '@/modules/tenders/components/tender-candidates/AssignToManagerPopover.vue';

const props = defineProps<{
  tableType: ConsultantTableType;
}>();

const fetchUrls = {
  [ConsultantTableType.AVAILABLE]: '/enterprise/candidates/bench',
  [ConsultantTableType.ENGAGED]: '/enterprise/candidates/assigned',
};

const loading = ref(true);
const candidates = ref<CandidateRow<typeof props.tableType>[]>([]);
const assignToManagerPopoverState = reactive({
  isOpened: false,
  candidate: undefined as CandidateRow<ConsultantTableType.ENGAGED> | undefined,
});

const consultantsTableColumns: NioTableColumn[] = [
  { key: 'name', label: 'Name', width: 1, align: 'left' as const },
  { key: 'profession', label: 'Profession', width: 2, align: 'left' as const },
  { key: 'rate', label: 'Rate', width: 1, align: 'left' as const },
  { key: 'seniority', label: 'Seniority', width: 1, align: 'left' as const },
  ...(props.tableType === ConsultantTableType.ENGAGED ? [{ key: 'managed-by', label: 'Managed by', width: 1, align: 'left' as const }] : []),
  { key: 'availability',
    label: props.tableType === ConsultantTableType.AVAILABLE ? 'Availability' : 'Engaged',
    width: 1,
    align: props.tableType === ConsultantTableType.ENGAGED ? 'left' as const : 'right' as const
  },
  ...(props.tableType === ConsultantTableType.ENGAGED ? [{ key: 'actions', label: 'Actions', width: 1, align: 'right' as const }] : []),
];

onMounted(async() => {
  loading.value = true;
  try {
    const response = await nioAxios.get<CandidatesResponse<typeof props.tableType>>(fetchUrls[props.tableType]);
    candidates.value = response.data.data;
  } catch {
    candidates.value = [];
  } finally {
    loading.value = false;
  }
});

const onAssignToManagerPressed = (candidate: CandidateRow<ConsultantTableType.ENGAGED>) => {
  assignToManagerPopoverState.candidate = candidate;
  assignToManagerPopoverState.isOpened = true;
};

const onUpdatedCandidate = (updatedCandidate: CandidateRow<ConsultantTableType.ENGAGED>) => {
  const idx = candidates.value.findIndex(v => v.id === updatedCandidate.id);
  if (idx !== -1) {
    candidates.value[idx] = updatedCandidate;
  }
};

const skeletonClass = 'w-full h-[22px] bg-nio-grey-100 rounded-10 mb-1';
</script>

<template>
  <div class="w-full">
    <NioTable v-if="loading" :columns="consultantsTableColumns">
      <NioTableRow v-for="i in 5" :key="i" :columns="consultantsTableColumns">
        <template #name>
          <div :class="skeletonClass" />
        </template>
        <template #profession>
          <div :class="skeletonClass" />
        </template>
        <template #rate>
          <div :class="skeletonClass" />
        </template>
        <template #seniority>
          <div :class="skeletonClass" />
        </template>
        <template #availability>
          <div :class="skeletonClass" />
        </template>
        <template v-if="tableType === ConsultantTableType.ENGAGED" #managed-by>
          <div :class="skeletonClass" />
        </template>
        <template v-if="tableType === ConsultantTableType.ENGAGED" #actions>
          <div :class="skeletonClass" />
        </template>
      </NioTableRow>
    </NioTable>

    <template v-else>
      <NioTable v-if="candidates?.length" :columns="consultantsTableColumns">
        <ConsultantsTableEntry
          v-for="(candidate, index) in candidates"
          :key="candidate?.id"
          :candidate-entry-data="candidate"
          :index="index"
          :table-type="tableType"
          :columns="consultantsTableColumns"
          @assign-to-manager="onAssignToManagerPressed(candidate as CandidateRow<ConsultantTableType.ENGAGED>)"
        />
      </NioTable>
      <div v-if="!candidates?.length" class="text-lg mb-4 text-center">
        No candidates found
      </div>
    </template>
    <Suspense>
      <AssignToManagerPopover
        v-model="assignToManagerPopoverState.isOpened"
        :candidate="assignToManagerPopoverState.candidate"
        @updated-candidate="onUpdatedCandidate"
      />
    </Suspense>
  </div>
</template>