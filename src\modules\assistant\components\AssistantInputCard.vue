<script setup lang="ts">
import { ref, watch, nextTick, computed } from 'vue';
import CloseIcon from '@/assets/icons/close-icon.svg';
import ArrowDownIcon from '@/assets/icons/arrow-down-white.svg';
import { useAssistantStore } from '@/modules/assistant/stores/assistant-store.ts';
import LoadingText from '@/modules/assistant/components/LoadingText.vue';
import { useRfpCacheStore } from '@/modules/assistant/stores/rfp-cache-store.ts';
import { useRouter } from 'vue-router';
import { routeMap } from '@/modules/assistant/routes';
import { nioAxios } from '@/axios.ts';
import type { DynamicFormStructure } from '@/common/utils/forms.ts';
import { useRfpFormCacheStore } from '@/modules/assistant/stores/rfp-form-cache-store.ts';
import { toast } from '@/common/utils/NotificationService';
import { AxiosError } from 'axios';

const assistantStore = useAssistantStore();
const rfpCacheStore = useRfpCacheStore();
const rfpFormCacheStore = useRfpFormCacheStore();
const router = useRouter();

interface FileItem {
  name: string;
  file: File;
}

const files = ref<FileItem[]>([]);
const fileInput = ref<HTMLInputElement | null>(null);
const isDragging = ref(false);
const isTextMode = ref(false);
const textContent = ref('');
const isLoading = ref(false);
const textAreaRef = ref<HTMLTextAreaElement | null>(null);
const cardHeight = ref(200);

const isCreateButtonDisabled = computed(() => {
  return !textContent.value.trim() && files.value.length === 0;
});

const allowedFileTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];

const processFiles = (selectedFiles: FileList | null) => {
  if (!selectedFiles) {
    return;
  }

  for (const file of selectedFiles) {
    if (!allowedFileTypes.includes(file.type)) {
      console.warn(`Nepovolený formát súboru: ${file.name}`);
      continue;
    }

    if (!files.value.some(f => f.file.name === file.name)) {
      files.value.push({ name: file.name, file });
    }
  }
  adjustTextAreaHeight();
};

const removeFile = (index: number) => {
  files.value.splice(index, 1);
  adjustTextAreaHeight();
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = true;
};

const handleDragLeave = () => {
  isDragging.value = false;
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = false;
  processFiles(event.dataTransfer?.files || null);
};

const openFileExplorer = () => {
  fileInput.value?.click();
};

const handleFileUpload = () => {
  processFiles(fileInput.value?.files || null);
  if (fileInput.value) {
    fileInput.value.value = '';
  }
  adjustTextAreaHeight();
};

const toggleTextMode = () => {
  isTextMode.value = !isTextMode.value;
  if (!isTextMode.value) {
    textContent.value = '';
  }
  adjustTextAreaHeight();
};

const startLoading = async() => {
  isLoading.value = true;

  try {
    const text = textContent.value || '';
    const responseData = await assistantStore.storeRfp(text, files.value.map(f => f.file));
    const infoResponse = await nioAxios.get<DynamicFormStructure>(`/enterprise/assistant/rfp/${responseData.id}/info`);
    rfpCacheStore.insert(responseData);
    rfpFormCacheStore.insert(infoResponse.data, responseData.id, 'info');
    await router.push({ name: routeMap.assistantRfp.name, params: { id: responseData.id } });
    textContent.value = '';
    files.value = [];
    isTextMode.value = false;
    cardHeight.value = 200;
  } catch (error) {
    if (error instanceof AxiosError && error.response?.status === 422) {
      const errorMessage = error.response.data.error?.message || error.response.data.message || 'Unknown validation error';
      toast.show('Upload error', errorMessage, 'error');
    } else {
      console.error('Error submitting RFP:', error);
    }
  } finally {
    isLoading.value = false;
  }
};

const adjustTextAreaHeight = () => {
  nextTick(() => {
    if (textAreaRef.value) {
      if (!textContent.value.trim()) {
        textAreaRef.value.style.height = files.value.length ? '50px' : '100px';
        cardHeight.value = 200;
        return;
      }
      textAreaRef.value.style.height = '0px';
      requestAnimationFrame(() => {
        const scrollHeight = textAreaRef.value!.scrollHeight;
        const baseHeight = files.value.length ? 150 : 100;
        const maxHeight = 300;
        let newHeight = Math.max(200, scrollHeight + baseHeight);
        newHeight = Math.min(newHeight, maxHeight);
        textAreaRef.value!.style.overflowY = newHeight < maxHeight ? 'hidden' : 'auto';
        textAreaRef.value!.style.height = `${newHeight - baseHeight}px`;
        cardHeight.value = newHeight;
      });
    }
  });
};

watch(textContent, adjustTextAreaHeight);
</script>

<template>
  <div
    class=" duration-300 ease-in-out"
  >
    <div
      class="w-[740px] rounded-30 border border-nio-blue-400 most-used-shadow p-[15px] flex flex-col z-40 overflow-hidden relative group"
      :style="{ height: `${cardHeight}px` }"
      :class="{ 'bg-nio-grey-background': !isDragging }"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
      @drop="handleDrop"
    >
      <div v-if="isLoading" class="flex flex-col origin-center h-full">
        <LoadingText />
      </div>

      <template v-else>
        <div v-if="isDragging" class="absolute inset-0 flex flex-col items-center justify-center pointer-events-none bg-nio-blue-800-10">
          <div class="w-8 h-8 flex items-center justify-center bg-nio-blue-800 rounded-full">
            <ArrowDownIcon class="w-4 h-4 text-white" />
          </div>
          <span
            class="mt-2 px-3 py-1 border border-nio-blue-400 text-nio-blue-800 text-[16px] font-medium leading-[20px] tracking-[-0.32px] rounded-10 bg-transparent"
          >
            Drop here to add
          </span>
        </div>

        <div
          v-if="files.length && !isDragging"
          class="flex overflow-x-auto space-x-2 pb-2 custom-scrollbar-01 ml-[15px] mr-[15px]"
        >
          <div
            v-for="(file, index) in files"
            :key="index"
            class="w-[202px] h-[40px] flex items-center justify-between bg-nio-grey-100 border border-nio-blue-400 rounded-10 px-[12px]"
          >
            <span class="text-[14px] font-medium text-nio-grey-500 leading-[18px] truncate">
              @ {{ file.name }}
            </span>
            <button
              class="w-[20px] aspect-square shrink-0 flex items-center justify-center bg-transparent border border-nio-blue-400 rounded-full hover:bg-nio-grey-background transition ml-[8px] hover:cursor-pointer"
              @click="removeFile(index)"
            >
              <CloseIcon class="h-2 w-2" />
            </button>
          </div>
        </div>

        <div
          v-if="!files.length && !isDragging && !isTextMode"
          class="flex flex-col items-center text-center mt-[50px]"
        >
          <div class="flex justify-center items-center space-x-2 mt-4">
            <button
              class="border border-dashed border-nio-grey-200 text-nio-grey-10 text-[16px] font-medium leading-[20px] tracking-[-0.32px] px-4 py-2 rounded-10"
            >
              Drag and drop
            </button>
            <span class="text-nio-grey-200 text-[16px] font-medium leading-[20px] tracking-[-0.32px]">or</span>
            <button
              class="border border-nio-grey-200 hover:cursor-pointer hover:border-nio-blue-800 text-nio-blue-800 text-[16px] font-medium leading-[20px] tracking-[-0.32px] px-4 py-2 rounded-10"
              @click="openFileExplorer"
            >
              Upload Documents
            </button>
          </div>
        </div>

        <div v-if="isTextMode" class="flex flex-col flex-1 min-h-0 m-[15px]">
          <textarea
            ref="textAreaRef"
            v-model="textContent"
            class="w-full flex-1 text-[14px] font-medium text-nio-grey-500 resize-none transition-all duration-300 ease-in-out outline-hidden focus:ring-0 bg-transparent custom-scrollbar-01 pt-1"
            :style="{ maxHeight: '300px' }"
            placeholder="Provide as much detail as possible about your project, including goals, requirements, and preferences. This helps us tailor recommendations and ensure the best matches for your needs."
          />
        </div>

        <div v-if="!isDragging" class="mt-auto flex">
          <button
            v-if="files.length || isTextMode"
            class="border border-nio-grey-hover-100 hover:bg-nio-grey-hover-100 hover:cursor-pointer text-nio-blue-800 text-[14px] font-medium leading-[16 px] tracking-[-0.32px] px-[14px] py-3 rounded-30"
            @click="openFileExplorer"
          >
            Upload Documents
          </button>
          <div class="flex ml-auto space-x-2">
            <button
              v-if="!isTextMode"
              class="bg-nio-grey-11 hover:bg-nio-grey-hover-100 hover:cursor-pointer text-nio-grey-700 text-[14px] font-medium leading-[16px] tracking-[-0.32px] px-[14px] py-3 rounded-30"
              @click="toggleTextMode"
            >
              Insert Text
            </button>
            <button
              v-if="files.length || isTextMode"
              class="bg-nio-blue-800 hover:bg-nio-blue-600-hover hover:cursor-pointer text-white text-[14px] font-medium leading-[16px] tracking-[-0.32px] px-[14px] py-3 rounded-30 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-nio-blue-800"
              :disabled="isCreateButtonDisabled"
              @click="startLoading"
            >
              Create
            </button>
          </div>
        </div>
      </template>

      <input
        ref="fileInput"
        type="file"
        class="hidden"
        accept=".pdf,.docx,.txt"
        multiple
        @change="handleFileUpload"
      >
    </div>
  </div>
</template>
