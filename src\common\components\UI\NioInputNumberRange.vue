<template>
  <div class="relative flex items-center">
    <Slider
      :id="inputId"
      v-model="model"
      class="range-vars w-[160px]"
      :merge="50"
      :format="formatTooltip"
      :min="inputData?.component?.min"
      :max="inputData?.component?.max"
      :disabled="disabled"
      :aria-describedby="`${inputData?.component?.payload_key}-errors${collectionIdentifier ?? ''}`"
    />
    <div
      v-if="error?.subKeys"
      :id="`${inputData?.component?.payload_key}-errors${collectionIdentifier ?? ''}`"
      class="absolute -bottom-3 text-[12px] -tracking-[0.024px] leading-4 font-medium text-nio-red-500 whitespace-nowrap"
      :class="errorPosition === 'right' ? 'right-0' : ''"
    >
      {{ error.subKeys.min }}<span v-if="error.subKeys.max">, {{ error.subKeys.max }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import Slider from '@vueform/slider';
import '@vueform/slider/themes/default.css';
import type { DynamicFormItemData, FormErrors } from '@/common/utils/forms.ts';
import { debounce } from 'lodash-es';
import { watch } from 'vue';

type Props = {
  disabled?: boolean,
  tooltipPrepend?: string,
  tooltipAppend?: string,
  error?: FormErrors[0],
  errorPosition?: 'left' | 'right',
  inputData?: DynamicFormItemData,
  inputId?: string,
  collectionIdentifier?: number,
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  tooltipPrepend: '',
  tooltipAppend: '',
  error: undefined,
  errorPosition: 'left',
  inputData: undefined,
  inputId: undefined,
  collectionIdentifier: undefined,
});

const emit = defineEmits(['nio-blur']);
const model = defineModel<[number, number]>({ required: true });

if (model.value[0] === null) {
  model.value[0] = props.inputData?.component?.min ?? 0;
}
if (model.value[1] === null) {
  model.value[1] = props.inputData?.component?.max ?? 0;
}

const formatTooltip = (value: string | number) => `${props.tooltipPrepend}${value}${props.tooltipAppend}`;

const debouncedChangedInput = debounce(() => emit('nio-blur'), 500);
watch(model, () => {
  debouncedChangedInput();
});
</script>

<style lang="css">
.range-vars {
  --slider-bg: var(--color-nio-white);
  --slider-connect-bg: var(--color-nio-white);
  --slider-connect-bg-disabled: var(--color-nio-grey-background-60);
  --slider-height: 16px;
  --slider-vertical-height: 300px;
  --slider-radius: 9999px;

  --slider-handle-bg: var(--color-nio-blue-500);
  --slider-handle-border: 0;
  --slider-handle-width: 20px;
  --slider-handle-height: 16px;
  --slider-handle-radius: 9999px;
  --slider-handle-shadow: 0;
  --slider-handle-shadow-active: 0 0;
  --slider-handle-ring-width: 3px;
  --slider-handle-ring-color: #10B98130;

  --slider-tooltip-font-size: 0.875rem;
  --slider-tooltip-line-height: 1.25rem;
  --slider-tooltip-font-weight: 600;
  --slider-tooltip-min-width: 20px;
  --slider-tooltip-bg: var(--color-nio-blue-500);
  --slider-tooltip-bg-disabled: var(--color-nio-blue-500);
  --slider-tooltip-color: #fff;
  --slider-tooltip-radius: 5px;
  --slider-tooltip-py: 2px;
  --slider-tooltip-px: 6px;
  --slider-tooltip-arrow-size: 5px;
  --slider-tooltip-distance: 3px;

  .slider-handle {
    top: 0;
  }

  .slider-handle:hover {
    --slider-handle-bg: var(--color-nio-blue-800);
  }

  .slider-handle:active {
    --slider-handle-bg: var(--color-nio-blue-500);
    box-shadow: 0px 0px 0px 2px var(--nio-blue-outline-stroke-400, #BBD0FB);
  }
}
</style>
