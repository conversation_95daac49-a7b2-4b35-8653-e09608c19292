<script setup lang="ts">
import { ref, computed } from 'vue';

import { useRouter } from 'vue-router';
import { routeMap } from '@/modules/companies/routes/';
import ConfirmationModal from '@/common/components/popover/ConfirmationModal.vue';
import type { Company } from '@/modules/companies/types/company';
import CompanyCategoryLabel from '@/modules/companies/components/CompanyCategoryLabel.vue';
import CompanyStatusLabel from '@/modules/companies/components/CompanyStatusLabel.vue';
import ThreeDots from '@/assets/icons/three-dots.svg';
import BasicPopover from '@/common/components/popover/BasicPopover.vue';

const modal = ref<InstanceType<typeof ConfirmationModal>>();

const showModal = () => {
  modal.value?.show();
};

const router = useRouter();
const emit = defineEmits(['deleteCompany']);

interface Props {
    company: Company;
}

const props = defineProps<Props>();

const handleVendorClick = (vendorId: string) => {
  router.push({ name: routeMap.vendorDetail.name, params: { id: vendorId } });
};

const cardPadding = 4;

const companyNoLogoText = computed(() => {
  const nameParts = props.company.name.split(' ');
  const firstChar = props.company.name.charAt(0);
  const secondChar = nameParts.length > 1 ? nameParts[nameParts.length - 1].charAt(0) : (props.company.name.length > 1 ? props.company.name.charAt(1) : '');
  return firstChar + secondChar;
});

const isImageLoaded = ref(true);
const isCoverLoaded = ref(true);

</script>

<template>
  <div
    class="group border border-nio-blue-outline-stroke-400 rounded-20 relative bg-nio-white w-full min-h-[250px]
    hover:shadow-lg hover:cursor-pointer hover:bg-nio-grey-background-90
    transition-all duration-100 ease-in-out"
    @click="handleVendorClick(company.id)"
  >
    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 w-7 h-7 rounded-30 flex items-center justify-center shadow-md bg-nio-grey-100 cursor-pointer">
      <BasicPopover class="inline-block">
        <div role="listbox" class="ml-auto w-7 h-7 rounded-full flex items-center justify-center border bg-nio-grey-background-90 border-nio-grey-200  hover:bg-nio-grey-100 cursor-pointer" title="Actions">
          <ThreeDots class="h-3 w-3" />
        </div>
        <template #popover-content>
          <div class="flex flex-col">
            <div
              class="text-sm justify-center text-center font-medium text-nio-grey-700 py-1 px-2 flex items-center gap-3 hover:bg-nio-grey-100 cursor-pointer rounded-10"
              role="option"
              @click.stop="showModal"
            >
              <div>Remove</div>
              <ConfirmationModal
                ref="modal"
                class="absolute text-center top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] cursor-auto"
                :modal-title="`Delete Vendor: ${company.name}`"
                modal-description="Do you really want to delete this vendor? This action cannot be undone."
                confirm-button-text="Delete"
                @confirmed.stop="emit('deleteCompany', company.id)"
              />
            </div>
          </div>
        </template>
      </BasicPopover>
    </div>
    <!-- cover -->
    <div class="h-[70px] w-full overflow-hidden bg-gradient-to-r from-nio-grey-700 to-nio-grey-900 rounded-t-20">
      <img
        v-if="company.cover && isCoverLoaded"
        :src="company.cover"
        alt=""
        class="w-full h-full object-cover"
        @error="isCoverLoaded = false"
      >
      <div v-else class="w-full h-full flex items-center justify-center bg-nio-grey-700 text-nio-white" />
    </div>

    <!-- wrapper -->
    <div class="relative">
      <!-- logo -->

      <div :class="`pl-${cardPadding} -mt-8 absolute`">
        <div class="w-15 h-15 overflow-hidden rounded-full align-middle shadow-md flex items-center justify-center bg-nio-grey-900 text-nio-grey-100 ">
          <img
            v-if="company.logo && isImageLoaded"
            :src="company.logo"
            :alt="company.name"
            class="object-cover w-full h-full"
            @error="isImageLoaded = false"
          >
          <span v-else class="text-nio-white relative flex items-center justify-center text-[20px]">
            {{ companyNoLogoText }}
          </span>
        </div>
      </div>

      <!-- status -->
      <div :class="`flex justify-end px-${cardPadding} pt-${cardPadding} w-full`">
        <CompanyStatusLabel :status="company.status" />
      </div>

      <div :class="`px-${cardPadding} pb-${cardPadding} pt-2`">
        <div class="text-h5 font-semibold text-black leading-normal line-clamp-2 break-all">
          {{ company.name }}
        </div>
        <div class="font-paragraph text-sm leading-normal text-nio-grey-500">
          {{ company.country }}
        </div>

        <hr class="border-nio-grey-100 mt-3 mb-3">

        <div class="flex flex-wrap items-center gap-2">
          <CompanyCategoryLabel :category="company.category" />
        </div>
      </div>
    </div>
  </div>
</template>