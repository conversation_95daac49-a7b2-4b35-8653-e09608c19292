<template>
  <div
    class="flex flex-col justify-between border border-nio-border-default rounded-20 p-5 bg-nio-white w-full"
  >
    <div class="flex flex-col">
      <div class="text-[14px] text-nio-grey-500 leading-[14px] mb-4">
        By
        <span class="text-[14px] text-nio-blue-800 leading-[14px]">
          @{{ author }}
        </span>
        <span class="text-[14px] text-nio-grey-500 leading-[14px] ml-1">
          {{ formattedDate }}
        </span>
      </div>

      <router-link
        :to="{name: 'detail', params: {id: props.id}}"
        class="text-[26px] font-normal text-nio-black-900 leading-[28px] tracking-[0.52px] mb-4 hover:underline"
      >
        {{ title }}
      </router-link>

      <p class="text-[18px] font-normal text-nio-grey-700 leading-[20px] tracking-[0.36px] line-clamp-2 mb-4">
        {{ description }}
      </p>

      <div class="flex flex-wrap gap-2 mt-1">
        <span class="px-3 py-1 text-[16px] font-normal text-nio-grey-700 rounded-5 bg-nio-grey-100 leading-4">
          {{ region }}
        </span>
        <span class="px-3 py-1 text-[16px] font-normal text-nio-grey-700 rounded-5 bg-nio-grey-100 leading-4">
          {{ mainIndustry }}
        </span>
      </div>
    </div>

    <div class="flex justify-end mt-4">
      <button
        class="px-4 py-2 bg-nio-blue-800 text-nio-white text-[14px] rounded-50 hover:bg-nio-blue-600-hover transition-colors cursor-pointer"
        @click="navigateToDetail"
      >
        Open
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { DateTime } from 'luxon';

interface Position {
  name?: string;
  count?: number;
}

const props = defineProps<{
  id: string;
  author?: string;
  date?: string;
  title: string;
  description?: string;
  region?: string;
  mainIndustry?: string;
  invitedVendors?: string;
  views?: string;
  applications?: string;
  positions?: Position[];
}>();

const router = useRouter();

const formattedDate = computed(() => {
  if (!props.date) {
    return '';
  }
  return DateTime.fromISO(props.date).toFormat('d LLLL yy');
});

const navigateToDetail = () => {
  router.push({ name: 'detail', params: { id: props.id } });
};
</script>
