import { Page } from '@playwright/test';
import { BasePage } from '../BasePage';

export class CompanyListPage extends BasePage {
  public readonly vendorCards;
  public readonly uploadButton;
  public readonly firstHeading;

  constructor(page: Page) {
    super(page);
    this.vendorCards = this.page.getByTestId('company-card');
    this.uploadButton = this.page.getByRole('link', { name: 'upload' });
    this.firstHeading = this.page.getByRole('heading').first();
  }

  async goto() {
    await this.page.goto('/');
    await this.mainMenuToggleButton.click();
    await this.navigation.getByRole('link', { name: 'Vendor Hub' }).click();
    await this.page.waitForURL('/companies');
  }
}
