<script setup lang="ts">
import { ref, watch } from 'vue';
import MagnifyingGlassIcon from '@/assets/icons/magnifying-glass-icon.svg';
import { XIcon } from 'lucide-vue-next';
import { debounce } from 'lodash-es';
import NioSwitch from '@/common/components/UI/NioSwitch.vue';
const props = defineProps<{
  searchQuery?: string;
  listView?: boolean;
}>();

const query = ref(props.searchQuery || '');
const isList = ref(props.listView || false);

const emit = defineEmits<{
  (e: 'update:searchQuery', value: string): void;
  (e: 'update:listView', value: boolean): void;
}>();

const debouncedEmit = debounce((newVal: string) => {
  emit('update:searchQuery', newVal);
}, 200);

watch(query, newVal => {
  debouncedEmit(newVal);
});

watch(isList, newVal => {
  emit('update:listView', newVal);
});
</script>

<template>
  <div class="flex gap-2 flex-wrap items-center">
    <!-- <button class="px-4 py-1.5 rounded-full text-sm transition-all duration-200 flex items-center gap-1.5 cursor-pointer bg-nio-grey-900 text-white">
      Filter
    </button> -->
    <div class="relative">
      <div class="absolute inset-y-0 left-3 flex items-center pointer-events-none">
        <MagnifyingGlassIcon class="w-4 h-4 text-gray-400" />
      </div>
      <input
        v-model="query"
        type="text"
        placeholder="Search..."
        class="w-64 h-9 pl-10 pr-10 py-2 bg-gray-100 text-gray-700 text-sm rounded-full border border-gray-300/40 focus:outline-none focus:ring-0 focus:bg-white transition-all duration-200 hover:bg-gray-50"
      >
      <button
        v-if="query"
        class="absolute inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
        @click="query = ''"
      >
        <XIcon class="w-4 h-4" />
      </button>
    </div>

    <NioSwitch v-model="isList" class="ml-auto" color="dark">
      <template #left>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="20"
          viewBox="0 0 16 14"
          fill="none"
        >
          <path
            d="M14.6667 1.8H10.2222V12.2H14.6667V1.8ZM5.77778 1.8H1.33333V6.13333H5.77778V1.8ZM7.11111 6.13333C7.11111 6.41224 7.11111 6.55169 7.08348 6.66678C6.9957 7.03243 6.71021 7.31792 6.34456 7.4057C6.22947 7.43333 6.09002 7.43333 5.81111 7.43333H1.3C1.02109 7.43333 0.881642 7.43333 0.766555 7.4057C0.400906 7.31792 0.115415 7.03243 0.0276301 6.66678C0 6.55169 0 6.41224 0 6.13333V1.8C0 1.52109 0 1.38164 0.0276301 1.26655C0.115415 0.900906 0.400906 0.615415 0.766555 0.52763C0.881642 0.5 1.02109 0.5 1.3 0.5H5.81111C6.09002 0.5 6.22947 0.5 6.34456 0.52763C6.71021 0.615415 6.9957 0.900906 7.08348 1.26655C7.11111 1.38164 7.11111 1.52109 7.11111 1.8V6.13333ZM16 12.2C16 12.4789 16 12.6184 15.9724 12.7334C15.8846 13.0991 15.5991 13.3846 15.2334 13.4724C15.1184 13.5 14.9789 13.5 14.7 13.5H10.1889C9.90998 13.5 9.77053 13.5 9.65544 13.4724C9.28979 13.3846 9.0043 13.0991 8.91652 12.7334C8.88889 12.6184 8.88889 12.4789 8.88889 12.2V1.8C8.88889 1.52109 8.88889 1.38164 8.91652 1.26655C9.0043 0.900906 9.28979 0.615415 9.65544 0.52763C9.77053 0.5 9.90998 0.5 10.1889 0.5H14.7C14.9789 0.5 15.1184 0.5 15.2334 0.52763C15.5991 0.615415 15.8846 0.900906 15.9724 1.26655C16 1.38164 16 1.52109 16 1.8V12.2Z"
            :fill="!isList ? 'white' : '#292929'"
          />
        </svg>
      </template>
      <template #right>
        <svg
          width="10"
          height="20"
          viewBox="0 0 10 8"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9.25 0.5L9.32715 0.503906C9.70512 0.542528 10 0.861833 10 1.25C10 1.63817 9.70512 1.95747 9.32715 1.99609L9.25 2H1.25C0.835786 2 0.5 1.66421 0.5 1.25C0.5 0.835786 0.835786 0.5 1.25 0.5H9.25Z"
            :fill="isList ? 'white' : '#292929'"
          />
          <path
            d="M9.25 6L9.32715 6.00391C9.70512 6.04253 10 6.36183 10 6.75C10 7.13817 9.70512 7.45747 9.32715 7.49609L9.25 7.5H1.25C0.835786 7.5 0.5 7.16421 0.5 6.75C0.5 6.33579 0.835786 6 1.25 6H9.25Z"
            :fill="isList ? 'white' : '#292929'"
          />
        </svg>
      </template>
    </NioSwitch>
  </div>
</template>
