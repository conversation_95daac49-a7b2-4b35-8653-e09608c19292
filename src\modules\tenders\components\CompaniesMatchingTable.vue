<script setup lang="ts">
import { computed } from 'vue';
import Loader from '@/common/components/Loader.vue';
import PreMatchingVendorDetail from '@/modules/tenders/components/PreMatchingVendorDetail.vue';
import type { Company } from '@/modules/tenders/types/tenders-types';
import NioScoreProgress from '@/common/components/NioScoreProgress.vue';
import { tenderScoreToPoints, tenderScoreToText } from '@/common/utils/score';
import { useTenderStore } from '../stores/tenders-store';
import InviteChip from '@/modules/tenders/components/InviteChip.vue';
import NioTableRow from '@/common/components/UI/table/NioTableRow.vue';
import NioTable from '@/common/components/UI/table/NioTable.vue';
import type { NioTableColumn } from '@/common/types/components';
import NioTableIndexCol from '@/common/components/UI/table/NioTableIndexCol.vue';

interface Props {
  companies: Company[];
  size: number;
  type: 'top' | 'good' | 'bad';
  startingIndex: number;
  tenderPublicId: string;
  matchId: string;
  loading: boolean;
  invitedCompanyIds?: string[];
}

const props = defineProps<Props>();
defineEmits<{
  (e: 'invite', companyId: string): void;
  (e: 'uninvite', companyId: string): void;
}>();

const displayedCompanies = computed(() => {
  return [...props.companies].sort((a, b) => {
    const aScore = a?.match_details?.overall_score ?? 0;
    const bScore = b?.match_details?.overall_score ?? 0;
    return bScore - aScore;
  });
});

const tenderStore = useTenderStore();
const inviteCompanyIds = computed(() => tenderStore.invitedVendors.filter(vendor => Boolean(vendor.sent_at)).map(vendor => vendor.id));
const pendingCompanyIds = computed(() => tenderStore.invitedVendors.filter(vendor => !vendor.sent_at).map(vendor => vendor.id));

const otherVendorsTableColumns: NioTableColumn[] = [
  { key: 'name', label: 'Company', width: 1, align: 'left' as const },
  { key: 'match', label: 'Match', width: 1, align: 'right' as const },
];
</script>

<template>
  <NioTable :columns="otherVendorsTableColumns">
    <template v-if="loading">
      <NioTableRow :columns="otherVendorsTableColumns">
        <template #name>
          <div class="w-[14px] h-[14px] bg-nio-grey-100 rounded-10 mb-4" />
          <div class="w-[98px] h-[22px] bg-nio-grey-100 rounded-10 mb-1" />
          <div class="w-[42px] h-[13px] bg-nio-grey-100 rounded-10" />
        </template>
        <template #match>
          <div class="w-[60px] h-[40px] bg-nio-grey-100 rounded-10" />
        </template>
      </NioTableRow>
    </template>
    <template v-else-if="displayedCompanies.length === 0">
      <div class="flex justify-center items-center h-full mt-[124px] mb-[127px]">
        <p
          class="text-nio-red-500 text-center font-paragraph text-[24px] px-[14px] py-[7px] rounded-15 border border-nio-blue-outline-stroke-400 shadow-custom-box-shadow-01 inline-block"
        >
          Unfortunately, none of the vendors met the required criteria.
        </p>
      </div>
    </template>
    <template v-else>
      <NioTableRow
        v-for="(company, index) in displayedCompanies"
        :key="index"
        :columns="otherVendorsTableColumns"
        expandable
      >
        <template #name>
          <div class="flex items-start space-x-sm">
            <NioTableIndexCol :index="startingIndex + index">
              <InviteChip
                :status="inviteCompanyIds?.includes(company.id) ? 'invited' : (pendingCompanyIds?.includes(company.id) ? 'pending' : 'not-invited')"
              />
              <p class="font-paragraph text-[18px] leading-normal text-nio-grey-900 truncate">
                {{ company?.name }}
              </p>
              <p class="font-paragraph text-sm leading-normal text-nio-grey-500">
                {{ company?.country }}, {{ company?.headquarters }}
              </p>
            </NioTableIndexCol>
          </div>
        </template>
        <template #match>
          <div class=" text-left relative group-hover:opacity-80 transition-all duration-200 flex items-center space-x-sm justify-end">
            <NioScoreProgress
              size="sm"
              :points="tenderScoreToPoints(company.match_details?.overall_score, 'total')"
              :title="tenderScoreToText(company.match_details?.overall_score)"
            />
          </div>
        </template>

        <template #expand>
          <keep-alive>
            <suspense>
              <PreMatchingVendorDetail
                :company="company"
                :invited="company.id !== undefined && props.invitedCompanyIds?.includes(String(company.id)) ? true : false"
                @invite="company.id !== undefined && $emit('invite', String(company.id))"
                @uninvite="company.id !== undefined && $emit('uninvite', String(company.id))"
              />
              <template #fallback>
                <div class="w-full flex justify-center col-span-full">
                  <Loader />
                </div>
              </template>
            </suspense>
          </keep-alive>
        </template>
      </NioTableRow>
    </template>
  </NioTable>
</template>

<style scoped>
.shadow-custom-box-shadow-01 {
  box-shadow: 0 71px 20px 0 rgba(207, 207, 207, 0.00),
  0 45px 18px 0 rgba(207, 207, 207, 0.01),
  0 25px 15px 0 rgba(207, 207, 207, 0.05),
  0 11px 11px 0 rgba(207, 207, 207, 0.09),
  0 3px 6px 0 rgba(207, 207, 207, 0.10);
}
</style>
